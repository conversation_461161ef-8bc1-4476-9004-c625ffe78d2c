<?php

namespace App\Filament\Resources\ScholarshipPrograms;

use App\Filament\Resources\ScholarshipPrograms\Pages\CreateScholarshipProgram;
use App\Filament\Resources\ScholarshipPrograms\Pages\EditScholarshipProgram;
use App\Filament\Resources\ScholarshipPrograms\Pages\ListScholarshipPrograms;
use App\Filament\Resources\ScholarshipPrograms\Pages\ViewScholarshipProgram;
use App\Filament\Resources\ScholarshipPrograms\Schemas\ScholarshipProgramForm;
use App\Filament\Resources\ScholarshipPrograms\Schemas\ScholarshipProgramInfolist;
use App\Filament\Resources\ScholarshipPrograms\Tables\ScholarshipProgramsTable;
use App\Models\ScholarshipProgram;
use BackedEnum;
use Filament\Resources\Resource;
use Filament\Schemas\Schema;
use Filament\Support\Icons\Heroicon;
use Filament\Tables\Table;

class ScholarshipProgramResource extends Resource
{
    protected static ?string $model = ScholarshipProgram::class;

    protected static string|BackedEnum|null $navigationIcon = Heroicon::OutlinedRectangleStack;

    public static function form(Schema $schema): Schema
    {
        return ScholarshipProgramForm::configure($schema);
    }

    public static function infolist(Schema $schema): Schema
    {
        return ScholarshipProgramInfolist::configure($schema);
    }

    public static function table(Table $table): Table
    {
        return ScholarshipProgramsTable::configure($table);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => ListScholarshipPrograms::route('/'),
            'create' => CreateScholarshipProgram::route('/create'),
            'view' => ViewScholarshipProgram::route('/{record}'),
            'edit' => EditScholarshipProgram::route('/{record}/edit'),
        ];
    }
}
