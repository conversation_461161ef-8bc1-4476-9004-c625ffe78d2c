<?php

namespace App\Filament\Resources\ScholarshipPrograms\Tables;

use Filament\Actions\BulkActionGroup;
use Filament\Actions\DeleteBulkAction;
use Filament\Actions\EditAction;
use Filament\Actions\ViewAction;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ScholarshipProgramsTable
{
    public static function configure(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('name')
                    ->searchable(),
                TextColumn::make('total_budget')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('per_student_budget')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('school_type_eligibility')
                    ->searchable(),
                TextColumn::make('min_gpa')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('min_units')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('semester')
                    ->searchable(),
                TextColumn::make('academic_year')
                    ->searchable(),
                TextColumn::make('application_deadline')
                    ->date()
                    ->sortable(),
                TextColumn::make('community_service_days')
                    ->numeric()
                    ->sortable(),
                IconColumn::make('active')
                    ->boolean(),
                TextColumn::make('created_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('updated_at')
                    ->dateTime()
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
                TextColumn::make('available_slots')
                    ->numeric()
                    ->sortable(),
            ])
            ->filters([
                //
            ])
            ->recordActions([
                ViewAction::make(),
                EditAction::make(),
            ])
            ->toolbarActions([
                BulkActionGroup::make([
                    DeleteBulkAction::make(),
                ]),
            ]);
    }
}
