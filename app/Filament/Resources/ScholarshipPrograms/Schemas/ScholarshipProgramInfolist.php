<?php

namespace App\Filament\Resources\ScholarshipPrograms\Schemas;

use Filament\Infolists\Components\IconEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class ScholarshipProgramInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('name'),
                TextEntry::make('total_budget')
                    ->numeric(),
                TextEntry::make('per_student_budget')
                    ->numeric(),
                TextEntry::make('school_type_eligibility'),
                TextEntry::make('min_gpa')
                    ->numeric(),
                TextEntry::make('min_units')
                    ->numeric(),
                TextEntry::make('semester'),
                TextEntry::make('academic_year'),
                TextEntry::make('application_deadline')
                    ->date(),
                TextEntry::make('community_service_days')
                    ->numeric(),
                IconEntry::make('active')
                    ->boolean(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
                TextEntry::make('available_slots')
                    ->numeric(),
            ]);
    }
}
