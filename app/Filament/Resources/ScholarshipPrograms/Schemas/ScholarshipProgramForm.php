<?php

namespace App\Filament\Resources\ScholarshipPrograms\Schemas;

use Filament\Forms\Components\DatePicker;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Schemas\Schema;

class ScholarshipProgramForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                Textarea::make('description')
                    ->required()
                    ->columnSpanFull(),
                TextInput::make('total_budget')
                    ->required()
                    ->numeric(),
                TextInput::make('per_student_budget')
                    ->required()
                    ->numeric(),
                TextInput::make('school_type_eligibility')
                    ->required(),
                TextInput::make('min_gpa')
                    ->required()
                    ->numeric(),
                TextInput::make('min_units')
                    ->numeric(),
                TextInput::make('semester')
                    ->required(),
                TextInput::make('academic_year')
                    ->required(),
                DatePicker::make('application_deadline')
                    ->required(),
                TextInput::make('community_service_days')
                    ->required()
                    ->numeric()
                    ->default(6),
                Toggle::make('active')
                    ->required(),
                TextInput::make('available_slots')
                    ->numeric(),
            ]);
    }
}
