<?php

namespace App\Filament\Resources\ScholarshipPrograms\Pages;

use App\Filament\Resources\ScholarshipPrograms\ScholarshipProgramResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;

class ListScholarshipPrograms extends ListRecords
{
    protected static string $resource = ScholarshipProgramResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make(),
        ];
    }
}
