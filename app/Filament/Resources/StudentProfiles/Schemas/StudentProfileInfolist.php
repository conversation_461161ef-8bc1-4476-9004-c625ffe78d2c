<?php

namespace App\Filament\Resources\StudentProfiles\Schemas;

use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class StudentProfileInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('user.name')
                    ->numeric(),
                TextEntry::make('address'),
                TextEntry::make('city'),
                TextEntry::make('state'),
                TextEntry::make('zip_code'),
                TextEntry::make('phone_number'),
                TextEntry::make('school_type'),
                TextEntry::make('school_level'),
                TextEntry::make('school_name'),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
                TextEntry::make('student_id'),
                TextEntry::make('gpa')
                    ->numeric(),
                TextEntry::make('email'),
                TextEntry::make('status'),
                TextEntry::make('first_name'),
                TextEntry::make('last_name'),
            ]);
    }
}
