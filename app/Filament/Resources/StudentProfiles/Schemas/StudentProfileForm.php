<?php

namespace App\Filament\Resources\StudentProfiles\Schemas;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class StudentProfileForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                Select::make('user_id')
                    ->relationship('user', 'name'),
                TextInput::make('address')
                    ->required(),
                TextInput::make('city')
                    ->required(),
                TextInput::make('state')
                    ->required(),
                TextInput::make('zip_code')
                    ->required(),
                TextInput::make('phone_number')
                    ->tel()
                    ->required(),
                TextInput::make('school_type')
                    ->required(),
                TextInput::make('school_level')
                    ->required(),
                TextInput::make('school_name')
                    ->required(),
                TextInput::make('student_id'),
                TextInput::make('gpa')
                    ->numeric(),
                TextInput::make('email')
                    ->email(),
                TextInput::make('status')
                    ->required()
                    ->default('unclaimed'),
                TextInput::make('first_name'),
                TextInput::make('last_name'),
            ]);
    }
}
