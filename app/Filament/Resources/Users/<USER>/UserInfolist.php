<?php

namespace App\Filament\Resources\Users\Schemas;

use Filament\Infolists\Components\ImageEntry;
use Filament\Infolists\Components\TextEntry;
use Filament\Schemas\Schema;

class UserInfolist
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextEntry::make('name'),
                TextEntry::make('email'),
                TextEntry::make('email_verified_at')
                    ->dateTime(),
                TextEntry::make('role'),
                TextEntry::make('status'),
                TextEntry::make('last_login_at')
                    ->dateTime(),
                TextEntry::make('created_at')
                    ->dateTime(),
                TextEntry::make('updated_at')
                    ->dateTime(),
                TextEntry::make('deleted_at')
                    ->dateTime(),
                TextEntry::make('avatar'),
                ImageEntry::make('cover_image'),
                TextEntry::make('facebook_id'),
                TextEntry::make('facebook_avatar'),
                TextEntry::make('facebook_profile_url'),
                TextEntry::make('provider'),
                TextEntry::make('provider_id'),
            ]);
    }
}
