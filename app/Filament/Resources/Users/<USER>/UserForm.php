<?php

namespace App\Filament\Resources\Users\Schemas;

use Filament\Forms\Components\DateTimePicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Schemas\Schema;

class UserForm
{
    public static function configure(Schema $schema): Schema
    {
        return $schema
            ->components([
                TextInput::make('name')
                    ->required(),
                TextInput::make('email')
                    ->email()
                    ->required(),
                DateTimePicker::make('email_verified_at'),
                TextInput::make('password')
                    ->password()
                    ->required(),
                TextInput::make('role')
                    ->required()
                    ->default('student'),
                TextInput::make('status')
                    ->required()
                    ->default('active'),
                DateTimePicker::make('last_login_at'),
                TextInput::make('avatar'),
                FileUpload::make('cover_image')
                    ->image(),
                TextInput::make('facebook_id'),
                TextInput::make('facebook_avatar'),
                TextInput::make('facebook_profile_url'),
                TextInput::make('provider'),
                TextInput::make('provider_id'),
                TextInput::make('provider_data'),
            ]);
    }
}
